# Gradle Properties
org.gradle.jvmargs = -Xmx3G

# Source Options
# Use Modern Java(9+) Syntax (Courtesy of Jabel)
use_modern_java_syntax = false

# Compilation Options
generate_sources_jar = true
generate_javadocs_jar = false

# Testing
enable_junit_testing = true
show_testing_output = false

# Mod Information
# HIGHLY RECOMMEND complying with SemVer for mod_version: https://semver.org/
mod_version = 1.0.0
root_package = com.example
mod_id = modid
mod_name = Mod Name

# Mod Metadata (Optional)
mod_description =
mod_url =
mod_update_json =
# Delimit authors with commas
mod_authors =
mod_credits =
mod_logo_path =

# Mapping Properties
mapping_channel = stable
mapping_version = 39
use_dependency_at_files = true

# Run Configurations
# If multiple arguments/tweak classes are stated, use spaces as the delimiter
minecraft_username = Developer
extra_jvm_args =
extra_tweak_classes =

# Maven Publishing (Provide secret: MAVEN_USER, MAVEN_PASS)
publish_to_maven = false
# Good for debugging artifacts before uploading to remote maven
# GitHub actions won't run if this is true, test this by running the task `publishToMavenLocal`
publish_to_local_maven = false
maven_name = ${mod_name}
maven_url =

# Publishing
# release_type can only be: release, beta or alpha (applies to CurseForge / Modrinth)
release_type = release
publish_with_changelog = ${{ it.file('CHANGELOG.md').exists() }}

# Publishing to CurseForge (Provide secret: CURSEFORGE_TOKEN)
# To configure dependencies, head to publishing.gradle's curseforge block
publish_to_curseforge = false
# CurseForge project ID must be the numerical ID and not the slug
curseforge_project_id =
curseforge_debug = false

# Publishing to Modrinth (Provide secret: MODRINTH_TOKEN), the token must have the `CREATE_VERSION` and `PROJECT_WRITE` permissions
# To configure dependencies, head to publishing.gradle's modrinth block
publish_to_modrinth = false
modrinth_project_id =
# Allows gradle to publish updated READMEs to the project body (via the modrinthSyncBody task)
modrinth_sync_readme = false
modrinth_debug = false

# If any properties changes below this line, refresh gradle again to ensure everything is working correctly.

# Modify Minecraft Sources
# RetroFuturaGradle allows Minecraft sources to be edited, and have the changes reflected upon running it
# Good for previews when coremodding, or generally seeing how behaviours can change with certain code applied/unapplied
# Turning this on allows Minecraft sources to persist and not regenerate
change_minecraft_sources = false

# Tags
# A RetroFuturaGradle concept akin to Ant ReplaceTokens
# A class is generated at build-time for compilation, to describe properties that have values that could change at build time such as versioning
# Class name is configurable with the `tag_class_name` property
# Tag properties can be stated in the `tags.properties` file, references are allowed
use_tags = true
tag_class_name = ${root_package}.${mod_id}.Tags

# Access Transformers
# A way to change visibility of Minecraft's classes, methods and fields
# An example access transformer file is given in the path: `src/main/resources/example_at.cfg`
# AT files should be in the root of src/main/resources with the filename formatted as: `mod_id_at.cfg`
# Use the property `access_transformer_locations` to state custom AT files if you aren't using the default `mod_id_at.cfg` location
# If multiple locations are stated, use spaces as the delimiter
use_access_transformer = false
access_transformer_locations = ${mod_id}_at.cfg

# Mixins
# Powerful tool to do runtime description changes of classes
# Wiki: https://github.com/SpongePowered/Mixin/wiki + https://github.com/CleanroomMC/MixinBooter/ + https://cleanroommc.com/wiki/forge-mod-development/mixin/preface
# Only use mixins once you understand the underlying structure
use_mixins = false
mixin_booter_version = 10.2
# A configuration defines a mixin set, and you may have as many mixin sets as you require for your application.
# Each config can only have one and only one package root.
# Generate missing configs, obtain from mixin_configs and generate file base on name convention: "mixins.config_name.json"
# You should change package root once they are generated
generate_mixins_json = true
# Delimit configs with spaces. Should only put configs name instead of full file name
mixin_configs = ${mod_id}
# A refmap is a json that denotes mapping conversions, this json is generated automatically, with the name `mixins.mod_id.refmap.json`
# Use the property `mixin_refmap` if you want it to use a different name, only one name is accepted
mixin_refmap = mixins.${mod_id}.refmap.json

# Coremods
# The most powerful way to change java classes at runtime, it is however very primitive with little documentation.
# Only make a coremod if you are absolutely sure of what you are doing
# Change the property `coremod_includes_mod` to false if your coremod doesn't have a @Mod annotation
# You MUST state a class name for `coremod_plugin_class_name` if you are making a coremod, the class should implement `IFMLLoadingPlugin`
is_coremod = false
coremod_includes_mod = true
coremod_plugin_class_name =

# AssetMover
# Convenient way to allow downloading of assets from official vanilla Minecraft servers, CurseForge, or any direct links
# Documentation: https://github.com/CleanroomMC/AssetMover
use_asset_mover = false
asset_mover_version = 2.5